import 'dart:async';

import 'package:bloomg_flutter/auth/models/user_model.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

/// {@template auth_repository_impl}
/// Implementation of [AuthRepository] for mock authentication.
/// {@endtemplate}
class AuthRepositoryImpl implements AuthRepository {
  /// {@macro auth_repository_impl}
  const AuthRepositoryImpl();

  static final LoggerService _logger = LoggerService();

  // Mock user for testing
  static const _mockUser = UserModel(
    uid: 'mock_uid_123',
    email: '<EMAIL>',
    displayName: 'Test User',
    emailVerified: true,
  );

  static final StreamController<UserModel> _userController =
      StreamController<UserModel>.broadcast();

  @override
  Stream<UserModel> get user => _userController.stream;

  @override
  UserModel get currentUser => _mockUser;

  @override
  Future<void> logInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    _logger.logFirebase(
      'Authentication request',
      'started',
      'Login attempt for: $email',
    );

    // Simulate network delay
    await Future<void>.delayed(const Duration(seconds: 2));

    // Mock authentication logic
    if (email.isEmpty || password.isEmpty) {
      _logger.logFirebase(
        'Authentication request',
        'failed',
        'Invalid credentials provided for: $email',
      );
      throw const LogInWithEmailAndPasswordFailure('Invalid credentials');
    }

    _logger.logFirebase(
      'Authentication request',
      'success',
      'Login successful for: $email',
    );

    // Emit authenticated user
    _userController.add(_mockUser);

    // For demo purposes, always succeed
    // In a real app, this would make an API call
  }

  @override
  Future<void> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    _logger.logFirebase(
      'User registration',
      'started',
      'Signup attempt for: $email (Name: $name)',
    );

    // Simulate network delay
    await Future<void>.delayed(const Duration(seconds: 2));

    // Mock sign up logic
    if (email.isEmpty || password.isEmpty || name.isEmpty) {
      _logger.logFirebase(
        'User registration',
        'failed',
        'Invalid information provided for: $email',
      );
      throw const SignUpWithEmailAndPasswordFailure('Invalid information');
    }

    _logger.logFirebase(
      'User registration',
      'success',
      'Signup successful for: $email (Name: $name)',
    );

    // Emit authenticated user
    _userController.add(
      _mockUser.copyWith(
        email: email,
        displayName: name,
      ),
    );

    // For demo purposes, always succeed
    // In a real app, this would make an API call
  }

  @override
  Future<void> sendPasswordResetEmail({
    required String email,
  }) async {
    _logger.logFirebase(
      'Password reset email',
      'started',
      'Reset request for: $email',
    );

    // Simulate network delay
    await Future<void>.delayed(const Duration(seconds: 2));

    // Mock password reset logic
    if (email.isEmpty) {
      _logger.logFirebase(
        'Password reset email',
        'failed',
        'Invalid email provided',
      );
      throw const SendPasswordResetEmailFailure('Invalid email');
    }

    _logger.logFirebase(
      'Password reset email',
      'success',
      'Reset email sent to: $email',
    );

    // For demo purposes, always succeed
    // In a real app, this would make an API call
  }

  @override
  Future<void> logOut() async {
    _logger.logFirebase(
      'User logout',
      'started',
      'Logout process initiated',
    );

    // Simulate network delay
    await Future<void>.delayed(const Duration(milliseconds: 500));

    _logger.logFirebase(
      'User logout',
      'success',
      'Logout completed successfully',
    );

    // Emit empty user (logged out)
    _userController.add(const UserModel.empty());

    // Mock logout logic
    // In a real app, this would clear tokens, etc.
  }
}
